#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应推理链 - 大模型为小模型生成推理步骤和决策路径
专门针对stats_mcp_server_official.py的统计查询场景进行优化
"""

import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
import time
from collections import defaultdict

from enhanced_stats_architecture import TaskComplexity, QueryContext, Model


@dataclass
class ReasoningStep:
    """推理步骤结构"""
    step_id: str
    description: str
    reasoning: str
    action: Optional[str] = None
    result: Optional[Any] = None
    confidence: float = 1.0


@dataclass
class ReasoningChain:
    """推理链结构"""
    chain_id: str
    query: str
    complexity: TaskComplexity
    steps: List[ReasoningStep]
    conclusion: Optional[str] = None
    metadata: Dict[str, Any] = None


class ReasoningTemplateLibrary:
    """推理模板库"""
    
    def __init__(self):
        # 预定义的推理模板
        self.templates = {
            TaskComplexity.SIMPLE: [
                {
                    "template_id": "simple_query",
                    "description": "简单查询推理链",
                    "steps": [
                        {"step_id": "parse_query", "description": "解析查询参数"},
                        {"step_id": "build_request", "description": "构建API请求"},
                        {"step_id": "execute_query", "description": "执行查询"},
                        {"step_id": "format_result", "description": "格式化结果"}
                    ]
                }
            ],
            TaskComplexity.MODERATE: [
                {
                    "template_id": "moderate_query",
                    "description": "中等复杂度查询推理链",
                    "steps": [
                        {"step_id": "analyze_intent", "description": "分析查询意图"},
                        {"step_id": "identify_parameters", "description": "识别查询参数"},
                        {"step_id": "validate_parameters", "description": "验证参数有效性"},
                        {"step_id": "optimize_query", "description": "优化查询策略"},
                        {"step_id": "execute_query", "description": "执行查询"},
                        {"step_id": "process_results", "description": "处理查询结果"},
                        {"step_id": "generate_explanation", "description": "生成结果解释"}
                    ]
                }
            ],
            TaskComplexity.COMPLEX: [
                {
                    "template_id": "complex_query",
                    "description": "复杂查询推理链",
                    "steps": [
                        {"step_id": "analyze_intent", "description": "深入分析查询意图"},
                        {"step_id": "decompose_query", "description": "将查询分解为子任务"},
                        {"step_id": "identify_parameters", "description": "识别所有查询参数"},
                        {"step_id": "validate_parameters", "description": "验证参数有效性"},
                        {"step_id": "optimize_query", "description": "优化查询策略"},
                        {"step_id": "plan_execution", "description": "规划执行步骤"},
                        {"step_id": "execute_subqueries", "description": "执行子查询"},
                        {"step_id": "aggregate_results", "description": "聚合子查询结果"},
                        {"step_id": "analyze_patterns", "description": "分析结果模式"},
                        {"step_id": "generate_insights", "description": "生成洞察"},
                        {"step_id": "create_explanation", "description": "创建详细解释"}
                    ]
                }
            ],
            TaskComplexity.CRITICAL: [
                {
                    "template_id": "critical_query",
                    "description": "关键查询推理链",
                    "steps": [
                        {"step_id": "deep_intent_analysis", "description": "深度意图分析"},
                        {"step_id": "context_gathering", "description": "收集上下文信息"},
                        {"step_id": "decompose_query", "description": "将查询分解为子任务"},
                        {"step_id": "parameter_identification", "description": "全面识别参数"},
                        {"step_id": "parameter_validation", "description": "严格验证参数"},
                        {"step_id": "query_optimization", "description": "多维度优化查询"},
                        {"step_id": "execution_planning", "description": "详细执行规划"},
                        {"step_id": "parallel_execution", "description": "并行执行子查询"},
                        {"step_id": "result_validation", "description": "验证结果有效性"},
                        {"step_id": "advanced_aggregation", "description": "高级结果聚合"},
                        {"step_id": "pattern_analysis", "description": "深度模式分析"},
                        {"step_id": "anomaly_detection", "description": "异常检测"},
                        {"step_id": "insight_generation", "description": "生成关键洞察"},
                        {"step_id": "comprehensive_explanation", "description": "全面解释"},
                        {"step_id": "recommendation_creation", "description": "创建行动建议"}
                    ]
                }
            ]
        }
        
        # 特定查询类型的模板
        self.query_type_templates = {
            "trend_analysis": {
                "description": "趋势分析推理链",
                "steps": [
                    {"step_id": "identify_time_range", "description": "确定时间范围"},
                    {"step_id": "select_time_granularity", "description": "选择时间粒度"},
                    {"step_id": "identify_metrics", "description": "确定分析指标"},
                    {"step_id": "execute_time_series_query", "description": "执行时间序列查询"},
                    {"step_id": "detect_trends", "description": "检测趋势模式"},
                    {"step_id": "identify_anomalies", "description": "识别异常点"},
                    {"step_id": "generate_trend_explanation", "description": "生成趋势解释"}
                ]
            },
            "comparison_analysis": {
                "description": "比较分析推理链",
                "steps": [
                    {"step_id": "identify_comparison_entities", "description": "确定比较实体"},
                    {"step_id": "select_comparison_metrics", "description": "选择比较指标"},
                    {"step_id": "normalize_data", "description": "数据标准化"},
                    {"step_id": "execute_comparison_queries", "description": "执行比较查询"},
                    {"step_id": "calculate_differences", "description": "计算差异"},
                    {"step_id": "identify_significant_differences", "description": "识别显著差异"},
                    {"step_id": "generate_comparison_explanation", "description": "生成比较解释"}
                ]
            },
            "anomaly_analysis": {
                "description": "异常分析推理链",
                "steps": [
                    {"step_id": "establish_baseline", "description": "建立基准"},
                    {"step_id": "identify_anomaly_period", "description": "确定异常时段"},
                    {"step_id": "execute_detailed_query", "description": "执行详细查询"},
                    {"step_id": "apply_anomaly_detection", "description": "应用异常检测算法"},
                    {"step_id": "correlate_with_events", "description": "与事件关联"},
                    {"step_id": "identify_root_causes", "description": "识别根本原因"},
                    {"step_id": "generate_anomaly_explanation", "description": "生成异常解释"}
                ]
            }
        }
    
    def get_template_for_complexity(self, complexity: TaskComplexity) -> Dict[str, Any]:
        """获取特定复杂度的模板"""
        templates = self.templates.get(complexity, [])
        if templates:
            return templates[0]  # 返回第一个匹配的模板
        else:
            # 返回默认模板
            return self.templates[TaskComplexity.SIMPLE][0]
    
    def get_template_for_query_type(self, query_type: str) -> Optional[Dict[str, Any]]:
        """获取特定查询类型的模板"""
        return self.query_type_templates.get(query_type)
    
    def merge_templates(self, base_template: Dict[str, Any], 
                       specialized_template: Dict[str, Any]) -> Dict[str, Any]:
        """合并模板"""
        # 创建基础模板的副本
        merged = base_template.copy()
        
        # 合并步骤，保持顺序
        base_steps = {step["step_id"]: step for step in base_template["steps"]}
        specialized_steps = {step["step_id"]: step for step in specialized_template["steps"]}
        
        # 合并步骤，保持基础模板的顺序，但使用专用模板的描述
        for i, step in enumerate(merged["steps"]):
            step_id = step["step_id"]
            if step_id in specialized_steps:
                merged["steps"][i] = specialized_steps[step_id]
        
        # 添加专用模板中的额外步骤
        for step_id, step in specialized_steps.items():
            if step_id not in base_steps:
                merged["steps"].append(step)
        
        # 更新模板描述
        merged["description"] = f"{base_template['description']} - {specialized_template['description']}"
        
        return merged


class ReasoningGenerator:
    """推理生成器 - 使用大模型生成推理步骤"""
    
    def __init__(self, large_model: Model):
        self.large_model = large_model
        self.template_library = ReasoningTemplateLibrary()
    
    async def generate_reasoning_chain(self, query: str, context: QueryContext, 
                                      domain_knowledge: List[str] = None) -> ReasoningChain:
        """生成推理链"""
        # 1. 获取基础模板
        base_template = self.template_library.get_template_for_complexity(context.complexity)
        
        # 2. 识别查询类型
        query_type = await self._identify_query_type(query)
        
        # 3. 获取专用模板（如果有）
        specialized_template = self.template_library.get_template_for_query_type(query_type)
        
        # 4. 合并模板（如果有专用模板）
        if specialized_template:
            template = self.template_library.merge_templates(base_template, specialized_template)
        else:
            template = base_template
        
        # 5. 使用大模型增强推理步骤
        enhanced_steps = await self._enhance_reasoning_steps(query, template["steps"], context, domain_knowledge)
        
        # 6. 创建推理链
        chain_id = f"chain_{int(time.time())}"
        reasoning_chain = ReasoningChain(
            chain_id=chain_id,
            query=query,
            complexity=context.complexity,
            steps=[
                ReasoningStep(
                    step_id=step["step_id"],
                    description=step["description"],
                    reasoning=step.get("reasoning", ""),
                    confidence=step.get("confidence", 1.0)
                )
                for step in enhanced_steps
            ],
            metadata={
                "query_type": query_type,
                "template_id": template["template_id"],
                "template_description": template["description"]
            }
        )
        
        return reasoning_chain
    
    async def _identify_query_type(self, query: str) -> str:
        """识别查询类型"""
        prompt = f"""
        分析以下统计查询的类型：
        查询: {query}
        
        请从以下类型中选择最匹配的一个：
        1. trend_analysis - 趋势分析，关注数据随时间的变化
        2. comparison_analysis - 比较分析，关注不同实体或时间段的对比
        3. anomaly_analysis - 异常分析，关注异常点和原因分析
        4. summary_statistics - 汇总统计，关注总体数据特征
        5. detailed_lookup - 详细查询，关注具体数据点
        
        只返回类型名称，不要有其他内容。
        """
        
        try:
            response = self.large_model([{"role": "user", "content": prompt}])
            query_type = response.content.strip().lower()
            
            # 标准化类型名称
            if "trend" in query_type:
                return "trend_analysis"
            elif "comparison" in query_type or "compare" in query_type:
                return "comparison_analysis"
            elif "anomaly" in query_type:
                return "anomaly_analysis"
            elif "summary" in query_type or "statistic" in query_type:
                return "summary_statistics"
            elif "detail" in query_type or "lookup" in query_type:
                return "detailed_lookup"
            else:
                return "general_query"
        except Exception as e:
            logging.warning(f"识别查询类型失败: {e}")
            return "general_query"
    
    async def _enhance_reasoning_steps(self, query: str, template_steps: List[Dict[str, Any]], 
                                     context: QueryContext, domain_knowledge: List[str] = None) -> List[Dict[str, Any]]:
        """增强推理步骤"""
        # 如果是简单查询，直接返回模板步骤
        if context.complexity == TaskComplexity.SIMPLE:
            return template_steps
        
        # 对于复杂查询，使用大模型增强推理步骤
        prompt = f"""
        为以下统计查询生成详细的推理步骤：
        
        查询: {query}
        复杂度: {context.complexity.value}
        
        基础推理步骤:
        {json.dumps([{"step_id": step["step_id"], "description": step["description"]} for step in template_steps], ensure_ascii=False, indent=2)}
        
        领域知识:
        {domain_knowledge[:2] if domain_knowledge else "无可用领域知识"}
        
        请为每个步骤添加详细的推理过程，说明在这一步需要考虑什么、如何执行以及预期的结果。
        
        以JSON格式返回增强后的步骤，每个步骤包含step_id、description和reasoning字段。
        """
        
        try:
            response = self.large_model([{"role": "user", "content": prompt}])
            enhanced_steps = json.loads(response.content)
            
            # 确保返回的是列表
            if isinstance(enhanced_steps, dict) and "steps" in enhanced_steps:
                enhanced_steps = enhanced_steps["steps"]
            
            # 确保每个步骤都有必要的字段
            for step in enhanced_steps:
                if "step_id" not in step:
                    step["step_id"] = f"step_{enhanced_steps.index(step)}"
                if "description" not in step:
                    step["description"] = f"步骤 {enhanced_steps.index(step) + 1}"
                if "reasoning" not in step:
                    step["reasoning"] = ""
            
            return enhanced_steps
        except Exception as e:
            logging.warning(f"增强推理步骤失败: {e}")
            # 返回原始模板步骤
            return template_steps


class ReasoningExecutor:
    """推理执行器 - 执行推理链中的步骤"""

    def __init__(self, small_model: Model, stats_client):
        self.small_model = small_model
        self.stats_client = stats_client
        self.execution_history = []

    async def execute_reasoning_chain(self, reasoning_chain: ReasoningChain,
                                    parsed_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行推理链"""
        execution_start_time = time.time()
        execution_results = []

        for step in reasoning_chain.steps:
            step_start_time = time.time()

            try:
                # 执行推理步骤
                step_result = await self._execute_step(step, parsed_params, execution_results)

                # 更新步骤结果
                step.result = step_result
                step.confidence = step_result.get('confidence', 1.0)

                execution_results.append({
                    'step_id': step.step_id,
                    'description': step.description,
                    'result': step_result,
                    'execution_time': time.time() - step_start_time
                })

            except Exception as e:
                logging.error(f"执行推理步骤 {step.step_id} 失败: {e}")
                step.result = {'error': str(e), 'success': False}
                step.confidence = 0.0

                execution_results.append({
                    'step_id': step.step_id,
                    'description': step.description,
                    'result': step.result,
                    'execution_time': time.time() - step_start_time
                })

        # 生成最终结论
        conclusion = await self._generate_conclusion(reasoning_chain, execution_results)
        reasoning_chain.conclusion = conclusion

        # 记录执行历史
        self.execution_history.append({
            'chain_id': reasoning_chain.chain_id,
            'query': reasoning_chain.query,
            'complexity': reasoning_chain.complexity.value,
            'execution_time': time.time() - execution_start_time,
            'success': all(result.get('result', {}).get('success', True) for result in execution_results)
        })

        return {
            'chain_id': reasoning_chain.chain_id,
            'execution_results': execution_results,
            'conclusion': conclusion,
            'total_execution_time': time.time() - execution_start_time
        }
